.game-end-view {
  height: calc(95% - 73px);

  .menu-left {
    .character-reveal {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .character-image-container {
        position: relative;
        
        .character-image {
          width: 183px !important;
          height: 183px !important;
          border-radius: 138px;
          border: 3px solid #88FFD5;
          box-shadow: 0px 0px 20px 0px #88FFD5;
          object-fit: cover;
          animation: character-reveal 1s ease-out;
        }
      }
    }
  }

  .game-end-content {
    justify-content: center !important;
    align-items: center;
    text-align: center;
    padding: 2rem;

    .result-section {
      max-width: 600px;
      
      .result-title {
        font-family: 'Playfair Display', serif;
        font-weight: 600;
        font-size: clamp(48px, 8vw, 80px);
        line-height: 1.2;
        margin-bottom: 2rem;
        text-shadow: 0 0 30px rgba(136, 255, 213, 0.6);
        animation: title-glow 2s ease-out;

        &.victory {
          color: #88FFD5;
          text-shadow: 0 0 30px rgba(136, 255, 213, 0.8);
        }

        &.defeat {
          color: #FF6B6B;
          text-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
        }

        &.draw {
          color: #FFD93D;
          text-shadow: 0 0 30px rgba(255, 217, 61, 0.6);
        }
      }

      .character-reveal-text {
        font-family: 'On Air', sans-serif;
        font-size: clamp(20px, 4vw, 28px);
        color: #e0e0e0;
        margin-bottom: 3rem;
        line-height: 1.4;
        opacity: 0.9;
      }

      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;

        .play-again-button {
          background: linear-gradient(135deg, #88FFD5, #40e0d0);
          color: #001428;
          border: none;
          border-radius: 28px;
          padding: 16px 40px;
          font-size: 22px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 20px rgba(136, 255, 213, 0.3);
          animation: button-pulse 2s infinite;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(136, 255, 213, 0.5);
            background: linear-gradient(135deg, #40e0d0, #88FFD5);
          }

          &:active {
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  .promotional-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 1rem;
    height: 100%;
    justify-content: center;

    .promo-card {
      background: rgba(0, 20, 40, 0.8);
      border: 1px solid rgba(136, 255, 213, 0.3);
      border-radius: 16px;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        border-color: rgba(136, 255, 213, 0.6);
        box-shadow: 0 8px 25px rgba(136, 255, 213, 0.2);
        transform: translateY(-2px);
      }

      .promo-image {
        margin-bottom: 1rem;
        
        img {
          width: 100%;
          max-width: 200px;
          height: 120px;
          object-fit: cover;
          border-radius: 12px;
          border: 1px solid rgba(136, 255, 213, 0.2);
        }
      }

      .promo-icon {
        margin-bottom: 1rem;
        display: flex;
        justify-content: center;
        
        img {
          width: 60px;
          height: 60px;
          border-radius: 12px;
        }
      }

      .promo-content {
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #88FFD5;
          margin-bottom: 0.5rem;
          line-height: 1.3;
        }

        p {
          font-size: 14px;
          color: #e0e0e0;
          margin-bottom: 1rem;
          opacity: 0.8;
        }

        .promo-button {
          background: #4A90E2;
          color: white;
          border: none;
          border-radius: 8px;
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          width: 100%;

          &:hover {
            background: #357ABD;
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// Animaciones
@keyframes character-reveal {
  0% {
    opacity: 0;
    transform: scale(0.8) rotateY(180deg);
    box-shadow: 0px 0px 0px 0px #88FFD5;
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotateY(90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
    box-shadow: 0px 0px 20px 0px #88FFD5;
  }
}

@keyframes title-glow {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes button-pulse {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(136, 255, 213, 0.3);
  }
  50% {
    box-shadow: 0 4px 30px rgba(136, 255, 213, 0.5);
  }
}

// Responsive design
@media (max-width: 768px) {
  .game-end-view {
    .game-end-content {
      padding: 1rem;
      
      .result-section {
        .result-title {
          font-size: clamp(32px, 6vw, 48px);
          margin-bottom: 1.5rem;
        }

        .character-reveal-text {
          font-size: clamp(16px, 3vw, 20px);
          margin-bottom: 2rem;
        }

        .action-buttons {
          .play-again-button {
            padding: 12px 24px;
            font-size: 18px;
          }
        }
      }
    }

    .promotional-content {
      gap: 1rem;
      padding: 0.5rem;

      .promo-card {
        padding: 1rem;

        .promo-content {
          h3 {
            font-size: 14px;
          }

          p {
            font-size: 12px;
          }

          .promo-button {
            font-size: 12px;
            padding: 6px 12px;
          }
        }
      }
    }
  }
}
