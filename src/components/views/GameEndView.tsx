import { useEnygmaGame } from "../../contexts/EnygmaGameContext";
import "./GameEndView.scss";

interface GameEndViewProps {
  onPlayAgain: () => void;
  onBackToMain: () => void;
}

const GameEndView: React.FC<GameEndViewProps> = ({
  onPlayAgain,
  // onBackToMain, // Not used yet but kept for future functionality
}) => {
  const { session } = useEnygmaGame();

  if (!session || session.phase !== "finished") {
    return null;
  }

  // Determinar si el usuario ganó
  const userWon = session.winner === "user";
  const isDraw = session.winner === undefined || session.winner === "draw";

  // Obtener el personaje revelado
  const revealedCharacter = session.currentCharacter || session.finalGuess || "Personaje desconocido";

  // Determinar el título según el resultado
  const getTitle = () => {
    if (userWon) {
      return "¡Has acertado!";
    } else if (isDraw) {
      return "¡Empate!";
    } else {
      return "¡No has acertado!";
    }
  };

  // Determinar el subtítulo
  const getSubtitle = () => {
    if (userWon) {
      return `El personaje es ${revealedCharacter}`;
    } else {
      return `El personaje era ${revealedCharacter}`;
    }
  };

  return (
    <div className="content game-end-view">
      <div className="menu-left">
        <div className="character-reveal">
          <div className="character-image-container">
            <img
              src="assets/game/character-placeholder.png"
              alt={revealedCharacter}
              className="character-image"
              onError={(e) => {
                // Fallback a imagen por defecto si no existe la imagen específica
                (e.target as HTMLImageElement).src = "assets/game/enygma.png";
              }}
            />
          </div>
        </div>
      </div>

      <div className="game game-end-content">
        <div className="result-section">
          <h1 className={`result-title ${userWon ? 'victory' : isDraw ? 'draw' : 'defeat'}`}>
            {getTitle()}
          </h1>

          <p className="character-reveal-text">
            {getSubtitle()}
          </p>

          <div className="action-buttons">
            <button
              className="play-again-button"
              onClick={onPlayAgain}
            >
              Volver a jugar
            </button>
          </div>
        </div>
      </div>

      <div className="menu-right promotional-content">
        <div className="promo-card">
          <div className="promo-image">
            <img
              src="assets/promo/los-anos-nuevos.jpg"
              alt="Los años nuevos"
              onError={(e) => {
                // Fallback a imagen por defecto
                (e.target as HTMLImageElement).src = "assets/game/placeholder-promo.png";
              }}
            />
          </div>
          <div className="promo-content">
            <h3>¿Te apetece ver contenido con Iria del Río?</h3>
            <p>Mira la serie "Los años nuevos"</p>
            <button className="promo-button">
              Comenzar a ver
            </button>
          </div>
        </div>

        <div className="promo-card">
          <div className="promo-icon">
            <img
              src="assets/icons/perplexity-icon.png"
              alt="Perplexity"
              onError={(e) => {
                // Fallback a icono por defecto
                (e.target as HTMLImageElement).src = "assets/game/placeholder-icon.png";
              }}
            />
          </div>
          <div className="promo-content">
            <h3>¿Quieres saber más sobre {revealedCharacter}?</h3>
            <p>Pregunta a Perplexity</p>
            <button className="promo-button">
              Abrir Perplexity
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameEndView;
